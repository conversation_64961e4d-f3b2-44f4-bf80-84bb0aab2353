/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/history` | `/history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner` | `/scanner`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/history/transaction-details` | `/history/transaction-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/dispensing-confirmation` | `/scanner/dispensing-confirmation`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/fuel-dispensing` | `/scanner/fuel-dispensing`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/manual-entry` | `/scanner/manual-entry`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/quota-validation` | `/scanner/quota-validation`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/transaction-success` | `/scanner/transaction-success`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/vehicle-details` | `/scanner/vehicle-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/about` | `/settings/about`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/app-settings` | `/settings/app-settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/profile-edit` | `/settings/profile-edit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/profile` | `/settings/profile`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/history` | `/history`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scanner` | `/scanner`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/history/transaction-details` | `/history/transaction-details`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scanner/dispensing-confirmation` | `/scanner/dispensing-confirmation`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scanner/fuel-dispensing` | `/scanner/fuel-dispensing`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scanner/manual-entry` | `/scanner/manual-entry`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scanner/quota-validation` | `/scanner/quota-validation`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scanner/transaction-success` | `/scanner/transaction-success`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scanner/vehicle-details` | `/scanner/vehicle-details`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings/about` | `/settings/about`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings/app-settings` | `/settings/app-settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings/profile-edit` | `/settings/profile-edit`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings/profile` | `/settings/profile`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/history${`?${string}` | `#${string}` | ''}` | `/history${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/notifications${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scanner${`?${string}` | `#${string}` | ''}` | `/scanner${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/history/transaction-details${`?${string}` | `#${string}` | ''}` | `/history/transaction-details${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scanner/dispensing-confirmation${`?${string}` | `#${string}` | ''}` | `/scanner/dispensing-confirmation${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scanner/fuel-dispensing${`?${string}` | `#${string}` | ''}` | `/scanner/fuel-dispensing${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scanner/manual-entry${`?${string}` | `#${string}` | ''}` | `/scanner/manual-entry${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scanner/quota-validation${`?${string}` | `#${string}` | ''}` | `/scanner/quota-validation${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scanner/transaction-success${`?${string}` | `#${string}` | ''}` | `/scanner/transaction-success${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scanner/vehicle-details${`?${string}` | `#${string}` | ''}` | `/scanner/vehicle-details${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings/about${`?${string}` | `#${string}` | ''}` | `/settings/about${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings/app-settings${`?${string}` | `#${string}` | ''}` | `/settings/app-settings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings/profile-edit${`?${string}` | `#${string}` | ''}` | `/settings/profile-edit${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings/profile${`?${string}` | `#${string}` | ''}` | `/settings/profile${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/history` | `/history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner` | `/scanner`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/history/transaction-details` | `/history/transaction-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/dispensing-confirmation` | `/scanner/dispensing-confirmation`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/fuel-dispensing` | `/scanner/fuel-dispensing`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/manual-entry` | `/scanner/manual-entry`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/quota-validation` | `/scanner/quota-validation`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/transaction-success` | `/scanner/transaction-success`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scanner/vehicle-details` | `/scanner/vehicle-details`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/about` | `/settings/about`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/app-settings` | `/settings/app-settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/profile-edit` | `/settings/profile-edit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings/profile` | `/settings/profile`; params?: Router.UnknownInputParams; };
    }
  }
}
