import React, { useState } from 'react';
import { View, StyleSheet, BackHandler } from 'react-native';
import { useFocusEffect } from 'expo-router';
import { router } from 'expo-router';
import QRScanner from '../../components/scanner/QRScanner';
import { apiService, storageService } from '../../services';

const QRScannerScreen: React.FC = () => {
  const [isActive, setIsActive] = useState(false);

  // Handle screen focus/blur
  useFocusEffect(
    React.useCallback(() => {
      setIsActive(true);

      // Handle Android back button
      const onBackPress = () => {
        handleClose();
        return true;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        setIsActive(false);
        subscription.remove();
      };
    }, [])
  );

  const handleQRCodeScanned = async (registrationNumber: string, ownerId: string) => {
    try {
      // Save to recent searches
      await storageService.saveRecentSearch(registrationNumber);

      // Get vehicle details by registration number
      const response = await apiService.getVehicleByRegistration(registrationNumber);

      if (response.error || !response.data) {
        // Navigate to manual entry with error
        router.push({
          pathname: '/(tabs)/scanner/manual-entry',
          params: {
            initialRegistrationNumber: registrationNumber,
            error: response.error || 'Vehicle not found',
          },
        });
        return;
      }

      const vehicleData = response.data;

      // Navigate to vehicle details
      router.push({
        pathname: '/(tabs)/scanner/vehicle-details',
        params: {
          vehicleId: vehicleData.vehicleId,
          registrationNumber: vehicleData.registrationNumber,
          vehicleData: JSON.stringify(vehicleData),
        },
      });
    } catch (error) {
      console.error('Error processing QR code:', error);
      router.push({
        pathname: '/(tabs)/scanner/manual-entry',
        params: {
          initialRegistrationNumber: registrationNumber,
          error: 'Failed to process QR code',
        },
      });
    }
  };

  const handleManualEntry = () => {
    router.push('/(tabs)/scanner/manual-entry');
  };

  const handleClose = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <QRScanner
        onQRCodeScanned={handleQRCodeScanned}
        onManualEntry={handleManualEntry}
        onClose={handleClose}
        isActive={isActive}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default QRScannerScreen;
