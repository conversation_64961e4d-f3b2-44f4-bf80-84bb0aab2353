import React, { useState } from 'react';
import { View, StyleSheet, BackHandler } from 'react-native';
import { useFocusEffect } from 'expo-router';
import { router } from 'expo-router';
import QRScanner from '../../components/scanner/QRScanner';
import { apiService, storageService } from '../../services';

const QRScannerScreen: React.FC = () => {
  const [isActive, setIsActive] = useState(false);

  // Handle screen focus/blur
  useFocusEffect(
    React.useCallback(() => {
      setIsActive(true);

      // Handle Android back button
      const onBackPress = () => {
        handleClose();
        return true;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        setIsActive(false);
        subscription.remove();
      };
    }, [])
  );

  const handleQRCodeScanned = async (registrationNumber: string, ownerId: string) => {
    try {
      // Save to recent searches
      await storageService.saveRecentSearch(registrationNumber);

      // Get vehicle details by registration number
      const response = await apiService.getVehicleByRegistration(registrationNumber);

      if (response.error || !response.data) {
        // For now, just log the error - we'll implement proper navigation later
        console.log('Vehicle not found:', registrationNumber, response.error);
        return;
      }

      const vehicleData = response.data;

      // For now, just log the success - we'll implement proper navigation later
      console.log('Vehicle found:', vehicleData);
    } catch (error) {
      console.error('Error processing QR code:', error);
    }
  };

  const handleManualEntry = () => {
    // For now, just log - we'll implement proper navigation later
    console.log('Manual entry pressed');
  };

  const handleClose = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <QRScanner
        onQRCodeScanned={handleQRCodeScanned}
        onManualEntry={handleManualEntry}
        onClose={handleClose}
        isActive={isActive}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default QRScannerScreen;
