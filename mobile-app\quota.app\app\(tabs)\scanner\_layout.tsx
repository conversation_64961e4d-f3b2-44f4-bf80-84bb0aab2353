import React from 'react';
import { Stack } from 'expo-router';

export default function ScannerLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="index" />
      <Stack.Screen name="manual-entry" />
      <Stack.Screen name="vehicle-details" />
      <Stack.Screen name="quota-validation" />
      <Stack.Screen name="fuel-dispensing" />
      <Stack.Screen name="dispensing-confirmation" />
      <Stack.Screen name="transaction-success" />
    </Stack>
  );
}
